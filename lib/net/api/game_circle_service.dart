import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/app_review_provider.dart';
import '../../providers/user_provider.dart';
import '../../utils/log_util.dart';
import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../sign_interceptor.dart';
import '../config/http_base_config.dart';
import '../../model/game_circle.dart';
import '../../model/game_circle_config.dart';
import '../../model/function_area.dart';
import '../../model/function_area_status.dart';
import 'init_service.dart';

class GameCircleService {
  static final GameCircleService _instance = GameCircleService._internal();
  factory GameCircleService() => _instance;
  GameCircleService._internal();
  static BuildContext? context = null;
  static  getAppkit(){
    final ticket = context?.read<UserProvider>().currentTicket;
    return ticket;
  }
  static getMuid(){
    final muid = context?.read<UserProvider>().currentUserId;
    return muid;
  }

  /// 获取游戏圈子列表接口
  /// 获取可用的游戏圈子列表
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<GameCircleListResponse>> getGameCircleList({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = 'game_circle/get_list';
      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(),
      };
      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<GameCircleListResponse>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        contentType: ContentType.form,
        data: queryParams,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return GameCircleListResponse.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<GameCircleListResponse>(),
      );

      LogUtil.d('游戏圈子列表响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('游戏圈子列表请求失败: $e');
      return BaseResponse<GameCircleListResponse>(
        code: 500,
        message: '游戏圈子列表请求失败: $e',
        data: null,
      );
    }
  }

  /// 获取游戏圈子配置信息接口
  /// 获取指定圈子的配置信息
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [tgid] 目标游戏ID
  /// [circleId] 圈子ID
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<GameCircleConfig>> getGameCircleConfig({
    BuildContext? context,
    required String tgid,
    required String circleId,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = 'game_circle/get_info';
      
      // 获取M层激活接口的公共参数
      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(),
        'tgid': tgid,
        'circle_id': circleId,
      };

      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<GameCircleConfig>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        data: queryParams,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          if (data is Map<String, dynamic>) {
            return GameCircleConfig.fromJson(data);
          }
          throw Exception('数据格式不正确');
        },
        parseStrategy: ResponseTransformers.stateData<GameCircleConfig>(),
      );

      LogUtil.d('游戏圈子配置响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('游戏圈子配置请求失败: $e');
      return BaseResponse<GameCircleConfig>(
        code: 500,
        message: '游戏圈子配置请求失败: $e',
        data: null,
      );
    }
  }

  /// 获取圈子功能区列表接口
  /// 获取圈子的功能区列表
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [tgid] 目标游戏ID
  /// [circleId] 圈子ID
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<List<FunctionArea>>> getFunctionAreaList({
    required BuildContext context,
    required String tgid,
    required String circleId,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = 'game_circle/get_active_area';

      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(),
        'tgid': tgid,
        'circle_id': circleId,
      };

      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<List<FunctionArea>>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        data: queryParams,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          // 接口返回的数据格式是 {"list": [...]}，需要提取 list 字段
          if (data is Map<String, dynamic> && data['list'] is List<dynamic>) {
            final list = data['list'] as List<dynamic>;
            return list
                .map((item) => FunctionArea.fromJson(item as Map<String, dynamic>))
                .toList();
          }
          throw Exception('数据格式不正确，期望 {"list": [...]}，实际: $data');
        },
        parseStrategy: ResponseTransformers.stateData<List<FunctionArea>>(),
      );

      LogUtil.d('圈子功能区列表响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('圈子功能区列表请求失败: $e');
      return BaseResponse<List<FunctionArea>>(
        code: 500,
        message: '圈子功能区列表请求失败: $e',
        data: null,
      );
    }
  }

  static List<FunctionArea>? processFunctionAreaList(
      BuildContext context,
      List<FunctionArea>? functionAreas) {
    if (functionAreas == null || functionAreas.isEmpty) {
      return functionAreas;
    }
    final appReviewProvider = Provider.of<AppReviewProvider>(context, listen: false);
    final isInReview = appReviewProvider.isInReview;
    if (isInReview) {
      return functionAreas.where((area) => area.areaModule != 'direct_pay').toList();
    }
    return functionAreas;
  }

  /// 获取圈子功能状态接口
  /// 获取圈子功能区的状态信息（角标等）
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [tgid] 目标游戏ID
  /// [circleId] 圈子ID
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<List<FunctionAreaStatus>>> getFunctionAreaStatus({
    BuildContext? context,
    required String tgid,
    required String circleId,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 接口URL
      const url = 'game_circle/get_area_status';

      final queryParams = <String, dynamic>{
        'app_ticket': getAppkit(),
        'muid': getMuid(),
        'tgid': tgid,
        'circle_id': circleId,
      };

      // 使用 HttpService 发送 POST 请求，指定 V3 签名
      final response = await HttpService.getInstance().post<List<FunctionAreaStatus>>(
        url,
        baseUrl: HttpBaseConfig.gameBaseUrl,
        data: queryParams,
        contentType: ContentType.form,
        signType: SignType.v3,
        fromJsonT: (data) {
          if (data == null) {
            throw Exception('响应数据为空');
          }
          // 接口返回的数据是对象数组
          if (data is List<dynamic>) {
            return data
                .map((item) => FunctionAreaStatus.fromJson(item as Map<String, dynamic>))
                .toList();
          }
          throw Exception('数据格式不正确，期望 List<dynamic>，实际: $data');
        },
        parseStrategy: ResponseTransformers.stateData<List<FunctionAreaStatus>>(),
      );

      LogUtil.d('圈子功能状态响应: ${response.code}, 数据: ${response.data}');
      return response;
    } catch (e) {
      LogUtil.e('圈子功能状态请求失败: $e');
      return BaseResponse<List<FunctionAreaStatus>>(
        code: 500,
        message: '圈子功能状态请求失败: $e',
        data: null,
      );
    }
  }
}